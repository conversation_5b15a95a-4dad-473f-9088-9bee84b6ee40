<?php

namespace App\Filament\Resources\SuperAdmin\OrganizationResource\Pages;

use App\Filament\Resources\SuperAdmin\OrganizationResource;
use Filament\Actions;
use Filament\Actions\Modal\Actions\Action;
use Filament\Resources\Pages\EditRecord;
use Illuminate\Support\Facades\Artisan;

class EditOrganization extends EditRecord
{
    protected static string $resource = OrganizationResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\DeleteAction::make(),
            Actions\Action::make('Update')->label('Бааз update хийх')
                ->requiresConfirmation()
                ->modalDescription('Та бааз шинэчлэхдээ итгэлтэй байна уу?')
                ->action(function () {
                    $db = $this->record->database_config->database;
                    Artisan::call('migrate', ['--path' => 'database/migrations/customer', '--database' => $db, '--force' => TRUE ]);           
                })->successNotificationTitle('Бааз update амжилттай дууслаа'),
        ];
    }
}
