<?php

namespace App\Filament\Resources\Admin\ConfigurationResource\Pages;

use App\Filament\Resources\Admin\ConfigurationResource;
use App\Filament\Components\ChronoAnchorComponent;
use App\Filament\Components\ChronoRangeComponent;
use App\Filament\Components\ChronoCountComponent;
use Filament\Actions;
use Filament\Resources\Pages\EditRecord;
use Filament\Notifications\Notification;
use Illuminate\Database\UniqueConstraintViolationException;

class EditConfiguration extends EditRecord
{
    protected static string $resource = ConfigurationResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\DeleteAction::make(),
        ];
    }

    protected function mutateFormDataBeforeFill(array $data): array
    {
        $record = $this->getRecord();

        if ($record) {
            // Parse VALUE field for vacation_anchor
            if ($record->value && $record->config_id === 'ATTENDANCE_STATUS_VACATION') {
                $parsed = ChronoAnchorComponent::parseFormula($record->value);
                $data['vacation_anchor_type'] = $parsed['type'];
                $data['vacation_anchor_shift_count'] = $parsed['shift_count'];
                $data['vacation_anchor_date_source'] = $parsed['date_source'];
                $data['vacation_anchor_custom_date'] = $parsed['custom_date'];
            }

            // Parse VALUE field for furlough_range
            if ($record->value && str_starts_with($record->config_id, 'ATTENDANCE_STATUS_FURLOUGH.')) {
                $parsed = ChronoRangeComponent::parseFormula($record->value);
                $data['furlough_range_type'] = $parsed['type'];
                $data['furlough_range_from'] = $parsed['from'];
                $data['furlough_range_to'] = $parsed['to'];
            }

            // Parse VALUE field for salary_furlough_count
            if ($record->value && str_starts_with($record->config_id, 'ATTENDANCE_STATUS_SALARY_FURLOUGH.')) {
                $parsed = ChronoCountComponent::parseFormula($record->value);
                $data['salary_furlough_count_static_type'] = $parsed['static_type'];
                $data['salary_furlough_count_chrono_type'] = $parsed['chrono_type'];
                $data['salary_furlough_count_length'] = $parsed['length'];
                $data['salary_furlough_count_count'] = $parsed['count'];
            }

            // Parse VALUE1 field for anchor_config
            if ($record->value1) {
                $parsed = ChronoAnchorComponent::parseFormula($record->value1);
                $data['anchor_config_type'] = $parsed['type'];
                $data['anchor_config_shift_count'] = $parsed['shift_count'];
                $data['anchor_config_date_source'] = $parsed['date_source'];
                $data['anchor_config_custom_date'] = $parsed['custom_date'];
            }
        }

        return $data;
    }

    protected function handleRecordUpdate($record, array $data): \Illuminate\Database\Eloquent\Model
    {
        try {
            return parent::handleRecordUpdate($record, $data);
        } catch (UniqueConstraintViolationException $e) {
            Notification::make()
                ->title('Алдаа')
                ->body('Тохиргооны ID давхардаж байна. Өөр утга оруулна уу.')
                ->danger()
                ->send();
            
            $this->halt();
        }
    }
}