<?php

namespace App\Models\Customer;

use App\Services\ConnectionService;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Configuration extends Model
{
    use HasFactory;

    const TABLE               = 'configurations';

    const ID                  = 'id';
    const CONFIG_ID           = 'config_id';
    const VALUE               = 'value';
    const VALUE1              = 'value1';
    const CREATED_BY          = 'created_by';
    const UPDATED_BY          = 'updated_by';

    public function __construct($attributes = array()) {
        $this->connection = ConnectionService::connectionName();
        parent::__construct($attributes);
    }

    /**
     * The attributes that are mass assignable.
     *
     * @var array
     */
    protected $fillable = [
        self::ID,
        self::CONFIG_ID,
        self::VALUE,
        self::VALUE1,
        self::CREATED_BY,
    ];
}
