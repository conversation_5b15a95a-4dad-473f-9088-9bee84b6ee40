<?php

namespace App\Services\Time;

use App\Models\Customer\Configuration;
use App\Models\Customer\Time\Attendance\EmployeeAttendanceDtl;
use App\Models\Customer\Employee;
use App\Models\Customer\EmployeeDtl;
use App\Services\ConnectionService;
use App\Filament\Components\ChronoAnchorComponent;
use App\Filament\Components\ChronoCountComponent;
use Carbon\Carbon;

class TimeRequestDateConstraintService
{
    /**
     * Get date constraints for a specific attendance status
     *
     * @param int $attStatus
     * @return array ['type' => string, 'min_date' => string|null, 'max_date' => string|null, 'message' => string]
     */
    public function getDateConstraints($attStatus)
    {
        try {
            // Get configuration ID using existing EmployeeAttendanceDtl method
            $employeeAttendanceDtl = app(EmployeeAttendanceDtl::class);
            $attStatusId = $employeeAttendanceDtl->getAttStatusId($attStatus);

            if (!$attStatusId) {
                return ['type' => 'none', 'min_date' => null, 'max_date' => null, 'message' => ''];
            }

            // Handle different attendance status types
            switch ($attStatusId) {
                case 'ATTENDANCE_STATUS_VACATION':
                    return $this->getVacationDateConstraints($attStatusId);
                    
                case 'ATTENDANCE_STATUS_OUT_WORK':
                case 'ATTENDANCE_STATUS_FURLOUGH.ATTENDANCE_STATUS_FURLOUGH_LONG':
                    return $this->getValue1DateConstraints($attStatusId);
                    
                case 'ATTENDANCE_STATUS_SALARY_FURLOUGH.ATTENDANCE_STATUS_SALARY_FURLOUGH_BIRTHDAY':
                case 'ATTENDANCE_STATUS_SALARY_FURLOUGH.ATTENDANCE_STATUS_SALARY_FURLOUGH_KID_BIRTHDAY':
                case 'ATTENDANCE_STATUS_SALARY_FURLOUGH.ATTENDANCE_STATUS_SALARY_FURLOUGH_HALFDAY':
                case 'ATTENDANCE_STATUS_SALARY_FURLOUGH.ATTENDANCE_STATUS_SALARY_FURLOUGH_NOSMOKE':
                    return $this->getSalaryFurloughDateConstraints($attStatusId);
                    
                default:
                    // For other statuses, try value1 configuration
                    return $this->getValue1DateConstraints($attStatusId);
            }

        } catch (\Exception $e) {
            \Log::error('TimeRequestDateConstraintService error: ' . $e->getMessage());
            return ['type' => 'none', 'min_date' => null, 'max_date' => null, 'message' => ''];
        }
    }



    /**
     * Get date constraints for vacation requests
     * Max date of (employee company_work_date + shift) and (value1 now date + shift)
     */
    private function getVacationDateConstraints($attStatusId)
    {
        \Log::info('TimeRequestDateConstraintService::getVacationDateConstraints - Start', [
            'attStatusId' => $attStatusId
        ]);

        $cn = ConnectionService::getCNForEmployeeUser();

        // Get vacation configuration using Eloquent
        $config = Configuration::on($cn)
            ->where(Configuration::CONFIG_ID, 'ATTENDANCE_STATUS_VACATION')
            ->first();

        \Log::info('TimeRequestDateConstraintService::getVacationDateConstraints - Configuration loaded', [
            'config_found' => !is_null($config),
            'config_value' => $config ? $config->value : null,
            'config_value1' => $config ? $config->value1 : null
        ]);

        if (!$config) {
            \Log::warning('TimeRequestDateConstraintService::getVacationDateConstraints - No configuration found');
            return ['type' => 'none', 'min_date' => null, 'max_date' => null, 'message' => ''];
        }

        $minDates = [];

        // Calculate minimum date from value (company work date + shift)
        if ($config->value) {
            $companyWorkDateMin = $this->calculateVacationCompanyWorkDateMin($config->value);
            if ($companyWorkDateMin) {
                $minDates[] = $companyWorkDateMin;
            }
        }

        // Calculate minimum date from value1 (now + shift)
        if ($config->value1) {
            $nowDateMin = $this->calculateAnchorMinDate($config->value1);
            if ($nowDateMin) {
                $minDates[] = $nowDateMin;
            }
        }

        if (empty($minDates)) {
            return ['type' => 'none', 'min_date' => null, 'max_date' => null, 'message' => ''];
        }

        // Return the maximum (latest) of all minimum dates
        $maxMinDate = collect($minDates)->max();

        $result = [
            'type' => 'min_date',
            'min_date' => $maxMinDate->format('Y-m-d'),
            'max_date' => null,
            'message' => 'Vacation requests must be after ' . $maxMinDate->format('Y-m-d')
        ];

        \Log::info('TimeRequestDateConstraintService::getVacationDateConstraints - End', [
            'result' => $result,
            'min_dates_count' => count($minDates),
            'max_min_date' => $maxMinDate->format('Y-m-d')
        ]);

        return $result;
    }

    /**
     * Get date constraints from value1 configuration only
     */
    private function getValue1DateConstraints($attStatusId)
    {
        \Log::info('TimeRequestDateConstraintService::getValue1DateConstraints - Start', [
            'attStatusId' => $attStatusId
        ]);

        $cn = ConnectionService::getCNForEmployeeUser();

        // For OUT_WORK and FURLOUGH_LONG, get configuration directly using Eloquent
        $config = Configuration::on($cn)
            ->where(Configuration::CONFIG_ID, $attStatusId)
            ->first();

        \Log::info('TimeRequestDateConstraintService::getValue1DateConstraints - Configuration loaded', [
            'config_found' => !is_null($config),
            'config_value1' => $config ? $config->value1 : null
        ]);

        if (!$config || !$config->value1) {
            \Log::warning('TimeRequestDateConstraintService::getValue1DateConstraints - No configuration or value1 found');
            return ['type' => 'none', 'min_date' => null, 'max_date' => null, 'message' => ''];
        }

        $minDate = $this->calculateAnchorMinDate($config->value1);

        \Log::info('TimeRequestDateConstraintService::getValue1DateConstraints - Min date calculated', [
            'min_date' => $minDate ? $minDate->format('Y-m-d') : null
        ]);

        if (!$minDate) {
            \Log::warning('TimeRequestDateConstraintService::getValue1DateConstraints - No min date calculated');
            return ['type' => 'none', 'min_date' => null, 'max_date' => null, 'message' => ''];
        }

        $result = [
            'type' => 'min_date',
            'min_date' => $minDate->format('Y-m-d'),
            'max_date' => null,
            'message' => 'Requests must be after ' . $minDate->format('Y-m-d')
        ];

        \Log::info('TimeRequestDateConstraintService::getValue1DateConstraints - End', [
            'result' => $result
        ]);

        return $result;
    }

    /**
     * Get date constraints for salary furlough requests
     * Max of value and value1 valid dates
     */
    private function getSalaryFurloughDateConstraints($attStatusId)
    {
        \Log::info('TimeRequestDateConstraintService::getSalaryFurloughDateConstraints - Start', [
            'attStatusId' => $attStatusId
        ]);

        $cn = ConnectionService::getCNForEmployeeUser();

        // Get salary furlough configuration using Eloquent
        $config = Configuration::on($cn)
            ->where(Configuration::CONFIG_ID, $attStatusId)
            ->first();

        \Log::info('TimeRequestDateConstraintService::getSalaryFurloughDateConstraints - Configuration loaded', [
            'config_found' => !is_null($config),
            'config_value' => $config ? $config->value : null,
            'config_value1' => $config ? $config->value1 : null
        ]);

        if (!$config) {
            \Log::warning('TimeRequestDateConstraintService::getSalaryFurloughDateConstraints - No configuration found');
            return ['type' => 'none', 'min_date' => null, 'max_date' => null, 'message' => ''];
        }

        $minDates = [];

        // Calculate minimum date from value1 (anchor-based)
        if ($config->value1) {
            $anchorMin = $this->calculateAnchorMinDate($config->value1);
            if ($anchorMin) {
                $minDates[] = $anchorMin;
            }
        }

        // Calculate minimum date from value (count-based - current session end date)
        if ($config->value) {
            $countMin = $this->calculateCountMinDate($config->value);
            if ($countMin) {
                $minDates[] = $countMin;
            }
        }

        \Log::info('TimeRequestDateConstraintService::getSalaryFurloughDateConstraints - Min dates calculated', [
            'min_dates_count' => count($minDates),
            'min_dates' => array_map(function($date) { return $date ? $date->format('Y-m-d') : null; }, $minDates)
        ]);

        if (empty($minDates)) {
            \Log::warning('TimeRequestDateConstraintService::getSalaryFurloughDateConstraints - No min dates calculated');
            return ['type' => 'none', 'min_date' => null, 'max_date' => null, 'message' => ''];
        }

        // Return the maximum (latest) of all minimum dates
        $maxMinDate = collect($minDates)->max();

        $result = [
            'type' => 'min_date',
            'min_date' => $maxMinDate->format('Y-m-d'),
            'max_date' => null,
            'message' => 'Salary furlough requests must be after ' . $maxMinDate->format('Y-m-d')
        ];

        \Log::info('TimeRequestDateConstraintService::getSalaryFurloughDateConstraints - End', [
            'result' => $result,
            'max_min_date' => $maxMinDate->format('Y-m-d')
        ]);

        return $result;
    }

    /**
     * Calculate minimum date from anchor configuration
     */
    private function calculateAnchorMinDate($anchorFormula)
    {
        try {
            $anchorData = ChronoAnchorComponent::parseFormula($anchorFormula);
            
            if (!$anchorData['type'] || $anchorData['shift_count'] === null) {
                return null;
            }

            // Use the same logic as TimeRequestValidationService
            $validationService = app(TimeRequestValidationService::class);
            $reflection = new \ReflectionClass($validationService);
            $method = $reflection->getMethod('calculateMinimumDate');
            $method->setAccessible(true);
            
            return $method->invoke($validationService, $anchorData);
            
        } catch (\Exception $e) {
            \Log::error('Error calculating anchor min date: ' . $e->getMessage());
            return null;
        }
    }

    /**
     * Calculate minimum date from count configuration (current session end date + 1 day)
     */
    private function calculateCountMinDate($countFormula)
    {
        try {
            $countData = ChronoCountComponent::parseFormula($countFormula);
            
            if (!$countData['chrono_type']) {
                return null;
            }

            $now = Carbon::now();
            
            // Use the same logic as TimeRequestValidationService
            $validationService = app(TimeRequestValidationService::class);
            $reflection = new \ReflectionClass($validationService);
            $method = $reflection->getMethod('getCurrentSessionEndDate');
            $method->setAccessible(true);
            
            $sessionEndDate = $method->invoke($validationService, $now, $countData['chrono_type']);
            
            if (!$sessionEndDate) {
                return null;
            }

            // Return the day after the current session ends
            return $sessionEndDate->addDay();
            
        } catch (\Exception $e) {
            \Log::error('Error calculating count min date: ' . $e->getMessage());
            return null;
        }
    }

    /**
     * Calculate minimum date for vacation based on company work date
     */
    private function calculateVacationCompanyWorkDateMin($anchorFormula)
    {
        try {
            $anchorData = ChronoAnchorComponent::parseFormula($anchorFormula);
            
            if (!$anchorData['type'] || $anchorData['shift_count'] === null || 
                $anchorData['date_source'] !== 'employee_dtl.company_work_date') {
                return null;
            }

            // Get current user's employee and employee_dtl
            $cn = ConnectionService::getCNForEmployeeUser();
            $user = auth()->user();
            
            if (!$user) {
                return null;
            }
            
            $employee = Employee::on($cn)->where(Employee::PHONE, $user->phone)->first();
            
            if (!$employee || !$employee->employee_dtl || !$employee->employee_dtl->company_work_date) {
                return null;
            }
            
            $companyWorkDate = Carbon::parse($employee->employee_dtl->company_work_date);
            
            // Use the same logic as TimeRequestValidationService
            $validationService = app(TimeRequestValidationService::class);
            $reflection = new \ReflectionClass($validationService);
            $method = $reflection->getMethod('applyShiftToDate');
            $method->setAccessible(true);
            
            return $method->invoke($validationService, $companyWorkDate, $anchorData['type'], $anchorData['shift_count']);
            
        } catch (\Exception $e) {
            \Log::error('Error calculating vacation company work date min: ' . $e->getMessage());
            return null;
        }
    }
}
