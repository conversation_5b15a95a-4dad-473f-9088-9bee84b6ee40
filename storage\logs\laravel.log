#0 /mnt/e/workspaces/astra/time/api/vendor/laravel/framework/src/Illuminate/Foundation/Bootstrap/HandleExceptions.php(255): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->handleError()
#1 /mnt/e/workspaces/astra/time/api/app/Models/Customer/Time/TimeRequest/TimeRequest.php(111): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->Illuminate\\Foundation\\Bootstrap\\{closure}()
#2 /mnt/e/workspaces/astra/time/api/vendor/laravel/framework/src/Illuminate/Support/Traits/ForwardsCalls.php(23): App\\Models\\Customer\\Time\\TimeRequest\\TimeRequest->getAttStatusName()
#3 /mnt/e/workspaces/astra/time/api/vendor/laravel/framework/src/Illuminate/Http/Resources/DelegatesToResource.php(155): Illuminate\\Http\\Resources\\Json\\JsonResource->forwardCallTo()
#4 /mnt/e/workspaces/astra/time/api/app/Http/Resources/TimeRequest.php(35): Illuminate\\Http\\Resources\\Json\\JsonResource->__call()
#5 /mnt/e/workspaces/astra/time/api/vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php(108): App\\Http\\Resources\\TimeRequest->toArray()
#6 /mnt/e/workspaces/astra/time/api/vendor/laravel/framework/src/Illuminate/Http/Resources/Json/ResourceResponse.php(39): Illuminate\\Http\\Resources\\Json\\JsonResource->resolve()
#7 /mnt/e/workspaces/astra/time/api/vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php(245): Illuminate\\Http\\Resources\\Json\\ResourceResponse->toResponse()
#8 /mnt/e/workspaces/astra/time/api/vendor/laravel/framework/src/Illuminate/Routing/Router.php(900): Illuminate\\Http\\Resources\\Json\\JsonResource->toResponse()
#9 /mnt/e/workspaces/astra/time/api/vendor/laravel/framework/src/Illuminate/Routing/Router.php(885): Illuminate\\Routing\\Router::toResponse()
#10 /mnt/e/workspaces/astra/time/api/vendor/laravel/framework/src/Illuminate/Routing/Router.php(805): Illuminate\\Routing\\Router->prepareResponse()
#11 /mnt/e/workspaces/astra/time/api/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(144): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}()
#12 /mnt/e/workspaces/astra/time/api/vendor/laravel/framework/src/Illuminate/Routing/Middleware/SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#13 /mnt/e/workspaces/astra/time/api/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle()
#14 /mnt/e/workspaces/astra/time/api/vendor/laravel/framework/src/Illuminate/Routing/Middleware/ThrottleRequests.php(159): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#15 /mnt/e/workspaces/astra/time/api/vendor/laravel/framework/src/Illuminate/Routing/Middleware/ThrottleRequests.php(125): Illuminate\\Routing\\Middleware\\ThrottleRequests->handleRequest()
#16 /mnt/e/workspaces/astra/time/api/vendor/laravel/framework/src/Illuminate/Routing/Middleware/ThrottleRequests.php(87): Illuminate\\Routing\\Middleware\\ThrottleRequests->handleRequestUsingNamedLimiter()
#17 /mnt/e/workspaces/astra/time/api/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Illuminate\\Routing\\Middleware\\ThrottleRequests->handle()
#18 /mnt/e/workspaces/astra/time/api/vendor/laravel/framework/src/Illuminate/Auth/Middleware/Authenticate.php(57): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#19 /mnt/e/workspaces/astra/time/api/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Illuminate\\Auth\\Middleware\\Authenticate->handle()
#20 /mnt/e/workspaces/astra/time/api/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#21 /mnt/e/workspaces/astra/time/api/vendor/laravel/framework/src/Illuminate/Routing/Router.php(805): Illuminate\\Pipeline\\Pipeline->then()
#22 /mnt/e/workspaces/astra/time/api/vendor/laravel/framework/src/Illuminate/Routing/Router.php(784): Illuminate\\Routing\\Router->runRouteWithinStack()
#23 /mnt/e/workspaces/astra/time/api/vendor/laravel/framework/src/Illuminate/Routing/Router.php(748): Illuminate\\Routing\\Router->runRoute()
#24 /mnt/e/workspaces/astra/time/api/vendor/laravel/framework/src/Illuminate/Routing/Router.php(737): Illuminate\\Routing\\Router->dispatchToRoute()
#25 /mnt/e/workspaces/astra/time/api/vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php(200): Illuminate\\Routing\\Router->dispatch()
#26 /mnt/e/workspaces/astra/time/api/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(144): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}()
#27 /mnt/e/workspaces/astra/time/api/vendor/livewire/livewire/src/Features/SupportDisablingBackButtonCache/DisableBackButtonCacheMiddleware.php(19): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#28 /mnt/e/workspaces/astra/time/api/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Livewire\\Features\\SupportDisablingBackButtonCache\\DisableBackButtonCacheMiddleware->handle()
#29 /mnt/e/workspaces/astra/time/api/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#30 /mnt/e/workspaces/astra/time/api/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle()
#31 /mnt/e/workspaces/astra/time/api/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle()
#32 /mnt/e/workspaces/astra/time/api/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#33 /mnt/e/workspaces/astra/time/api/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/TrimStrings.php(40): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle()
#34 /mnt/e/workspaces/astra/time/api/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle()
#35 /mnt/e/workspaces/astra/time/api/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#36 /mnt/e/workspaces/astra/time/api/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize->handle()
#37 /mnt/e/workspaces/astra/time/api/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/PreventRequestsDuringMaintenance.php(99): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#38 /mnt/e/workspaces/astra/time/api/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle()
#39 /mnt/e/workspaces/astra/time/api/vendor/laravel/framework/src/Illuminate/Http/Middleware/HandleCors.php(62): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#40 /mnt/e/workspaces/astra/time/api/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Illuminate\\Http\\Middleware\\HandleCors->handle()
#41 /mnt/e/workspaces/astra/time/api/vendor/laravel/framework/src/Illuminate/Http/Middleware/TrustProxies.php(39): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#42 /mnt/e/workspaces/astra/time/api/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Illuminate\\Http\\Middleware\\TrustProxies->handle()
#43 /mnt/e/workspaces/astra/time/api/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#44 /mnt/e/workspaces/astra/time/api/vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php(175): Illuminate\\Pipeline\\Pipeline->then()
#45 /mnt/e/workspaces/astra/time/api/vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter()
#46 /mnt/e/workspaces/astra/time/api/public/index.php(51): Illuminate\\Foundation\\Http\\Kernel->handle()
#47 /mnt/e/workspaces/astra/time/api/vendor/laravel/framework/src/Illuminate/Foundation/resources/server.php(16): require_once('...')
#48 {main}
"} 
[2025-08-26 11:31:52] local.INFO: TimeRequestDateConstraintService::getSalaryFurloughDateConstraints - Start {"attStatusId":"ATTENDANCE_STATUS_SALARY_FURLOUGH.ATTENDANCE_STATUS_SALARY_FURLOUGH_BIRTHDAY"} 
[2025-08-26 11:31:52] local.INFO: TimeRequestDateConstraintService::getSalaryFurloughDateConstraints - Configuration loaded {"config_found":true,"config_value":"count,year,hour,4,1","config_value1":"anchor,now,day,1"} 
[2025-08-26 11:31:52] local.INFO: TimeRequestDateConstraintService::calculateAnchorMinDate - Start {"anchorFormula":"anchor,now,day,1"} 
[2025-08-26 11:31:52] local.INFO: TimeRequestDateConstraintService::calculateAnchorMinDate - Formula parsed {"anchorData":{"type":"day","shift_count":1,"date_source":"now","custom_date":null}} 
[2025-08-26 11:31:52] local.INFO: TimeRequestDateConstraintService::calculateAnchorMinDate - End {"result":"2025-08-27 11:31:52"} 
[2025-08-26 11:31:52] local.INFO: TimeRequestDateConstraintService::calculateCountMinDate - Start {"countFormula":"count,year,hour,4,1"} 
[2025-08-26 11:31:52] local.INFO: TimeRequestDateConstraintService::calculateCountMinDate - Formula parsed {"countData":{"static_type":"year","chrono_type":"hour","length":4,"count":1}} 
[2025-08-26 11:31:52] local.INFO: TimeRequestDateConstraintService::calculateCountMinDate - Processing {"now":"2025-08-26 11:31:52","chrono_type":"hour"} 
[2025-08-26 11:31:52] local.INFO: TimeRequestDateConstraintService::calculateCountMinDate - Session end date calculated {"sessionEndDate":null} 
[2025-08-26 11:31:52] local.WARNING: TimeRequestDateConstraintService::calculateCountMinDate - No session end date calculated  
[2025-08-26 11:31:52] local.INFO: TimeRequestDateConstraintService::getSalaryFurloughDateConstraints - Min dates calculated {"min_dates_count":1,"min_dates":["2025-08-27"]} 
[2025-08-26 11:31:52] local.INFO: TimeRequestDateConstraintService::getSalaryFurloughDateConstraints - End {"result":{"type":"min_date","min_date":"2025-08-27","max_date":null,"message":"Salary furlough requests must be after 2025-08-27"},"max_min_date":"2025-08-27"} 
