<?php

namespace App\Services\Time;

use App\Models\Customer\Configuration;
use App\Models\Customer\Time\Attendance\EmployeeAttendanceDtl;
use App\Models\Customer\Time\TimeRequest\TimeRequest;
use App\Models\Customer\Employee;
use App\Models\Customer\EmployeeDtl;
use App\Services\ConnectionService;
use App\Filament\Components\ChronoAnchorComponent;
use App\Filament\Components\ChronoCountComponent;
use App\Filament\Components\ChronoRangeComponent;
use App\Http\Tools\DateTool;
use Carbon\Carbon;

class TimeRequestValidationService
{
    /**
     * Validate begin_date based on att_status and configuration settings (value1 field)
     *
     * @param string $beginDate
     * @param int $attStatus
     * @return array ['valid' => bool, 'message' => string]
     */
    public function validateValue1($beginDate, $attStatus)
    {
        try {
            // Get configuration ID using existing EmployeeAttendanceDtl method
            $employeeAttendanceDtl = app(EmployeeAttendanceDtl::class);
            $attStatusId = $employeeAttendanceDtl->getAttStatusId($attStatus);

            if (!$attStatusId) {
                // No status ID found for this att_status, allow the request
                return ['valid' => true, 'message' => ''];
            }

            // Get configuration from database
            $cn = ConnectionService::getCNForEmployeeUser();
            $configuration = Configuration::on($cn)
                ->where(Configuration::CONFIG_ID, $attStatusId)
                ->first();

            if (!$configuration || !$configuration->value1) {
                // No configuration or value1 found, allow the request
                return ['valid' => true, 'message' => ''];
            }

            // Parse the anchor formula from value1
            $anchorData = ChronoAnchorComponent::parseFormula($configuration->value1);
            
            if (!$anchorData['type'] || $anchorData['shift_count'] === null) {
                // Invalid formula, allow the request
                return ['valid' => true, 'message' => ''];
            }

            // Calculate the minimum allowed date based on anchor configuration
            $minimumDate = $this->calculateMinimumDate($anchorData);
            
            if (!$minimumDate) {
                // Could not calculate minimum date, allow the request
                return ['valid' => true, 'message' => ''];
            }

            // Compare begin_date with minimum allowed date
            $beginDateCarbon = Carbon::parse($beginDate);
            if ($beginDateCarbon->lt($minimumDate)) {
                $attStatusName = $this->getAttStatusName($attStatus);
                $minimumDateFormatted = $minimumDate->format('Y-m-d');
                return [
                    'valid' => false,
                    'message' => "Эхлэх огноо {$attStatusName}-ийн хувьд {$minimumDateFormatted}-ээс эрт байж болохгүй."
                ];
            }

            return ['valid' => true, 'message' => ''];

        } catch (\Exception $e) {
            // Log error but don't block the request
            \Log::error('TimeRequestValidationService error: ' . $e->getMessage());
            return ['valid' => true, 'message' => ''];
        }
    }

    /**
     * Validate begin_date based on att_status and configuration settings (value field)
     * Handles specific salary furlough configurations with count and quarter-based validation
     * Also handles furlough duration validation for SHORT/MEDIUM/LONG types
     *
     * @param string $beginDate
     * @param int $attStatus
     * @param string|null $endDate
     * @return array ['valid' => bool, 'message' => string]
     */
    public function validateValue($beginDate, $attStatus, $endDate = null)
    {
        try {
            // Get configuration ID using existing EmployeeAttendanceDtl method
            $employeeAttendanceDtl = app(EmployeeAttendanceDtl::class);
            $attStatusId = $employeeAttendanceDtl->getAttStatusId($attStatus);

            if (!$attStatusId) {
                // No status ID found for this att_status, allow the request
                return ['valid' => true, 'message' => ''];
            }

            // Check if this is ATTENDANCE_STATUS_VACATION, salary furlough, or regular furlough configurations
            $salaryFurloughStatusIds = [
                'ATTENDANCE_STATUS_SALARY_FURLOUGH_BIRTHDAY',
                'ATTENDANCE_STATUS_SALARY_FURLOUGH_KID_BIRTHDAY',
                'ATTENDANCE_STATUS_SALARY_FURLOUGH_HALFDAY',
                'ATTENDANCE_STATUS_SALARY_FURLOUGH_NOSMOKE'
            ];

            $furloughStatusIds = [
                'ATTENDANCE_STATUS_FURLOUGH_SHORT',
                'ATTENDANCE_STATUS_FURLOUGH_MEDIUM',
                'ATTENDANCE_STATUS_FURLOUGH_LONG'
            ];

            $isVacation = ($attStatusId === 'ATTENDANCE_STATUS_VACATION');
            $isSalaryFurlough = in_array($attStatusId, $salaryFurloughStatusIds);
            $isFurlough = in_array($attStatusId, $furloughStatusIds);

            if (!$isVacation && !$isSalaryFurlough && !$isFurlough) {
                // Not a vacation, salary furlough, or furlough config, allow the request
                return ['valid' => true, 'message' => ''];
            }

            // Map to the appropriate configuration ID
            if ($isVacation) {
                $configId = 'ATTENDANCE_STATUS_VACATION';
            } elseif ($isSalaryFurlough) {
                $configId = 'ATTENDANCE_STATUS_SALARY_FURLOUGH.' . $attStatusId;
            } else { // $isFurlough
                $configId = 'ATTENDANCE_STATUS_FURLOUGH.' . $attStatusId;
            }

            // Get configuration from database
            $cn = ConnectionService::getCNForEmployeeUser();
            $configuration = Configuration::on($cn)
                ->where(Configuration::CONFIG_ID, $configId)
                ->first();

            if (!$configuration || !$configuration->value) {
                // No configuration or value found, allow the request
                return ['valid' => true, 'message' => ''];
            }

            if ($isVacation) {
                // Handle vacation validation using ChronoAnchor
                return $this->validateVacationRequest($beginDate, $configuration->value, $attStatus);
            } elseif ($isSalaryFurlough) {
                // Handle salary furlough validation using ChronoCount
                $countData = ChronoCountComponent::parseFormula($configuration->value);

                if (!$countData['static_type'] || !$countData['chrono_type'] ||
                    $countData['length'] === null || $countData['count'] === null) {
                    // Invalid formula, allow the request
                    return ['valid' => true, 'message' => ''];
                }

                // Validate based on count and quarter logic
                $validationResult = $this->validateCountAndQuarter($beginDate, $countData, $attStatus);

                return $validationResult;
            } else { // $isFurlough
                // Handle furlough duration validation using ChronoRange
                return $this->validateFurloughDuration($beginDate, $endDate, $configuration->value, $attStatus);
            }

        } catch (\Exception $e) {
            // Log error but don't block the request
            \Log::error('TimeRequestValidationService validateValue error: ' . $e->getMessage());
            return ['valid' => true, 'message' => ''];
        }
    }

    /**
     * Validate vacation request using ChronoAnchor configuration
     *
     * @param string $beginDate
     * @param string $configValue
     * @param int $attStatus
     * @return array ['valid' => bool, 'message' => string]
     */
    private function validateVacationRequest($beginDate, $configValue, $attStatus)
    {
        try {
            // Parse the anchor formula from value field
            $anchorData = ChronoAnchorComponent::parseFormula($configValue);

            if (!$anchorData['type'] || $anchorData['shift_count'] === null) {
                // Invalid formula, allow the request
                return ['valid' => true, 'message' => ''];
            }

            $beginDateCarbon = Carbon::parse($beginDate);

            // Handle employee_dtl.company_work_date as date source
            if ($anchorData['date_source'] === 'employee_dtl.company_work_date') {
                $validationResult = $this->validateVacationWithCompanyWorkDate($beginDateCarbon, $anchorData, $attStatus);
                if (!$validationResult['valid']) {
                    return $validationResult;
                }
            } else {
                // Handle other date sources (now, custom date, etc.)
                $minimumDate = $this->calculateMinimumDate($anchorData);

                if ($minimumDate && $beginDateCarbon->lt($minimumDate)) {
                    $attStatusName = $this->getAttStatusName($attStatus);
                    $minimumDateFormatted = $minimumDate->format('Y-m-d');
                    return [
                        'valid' => false,
                        'message' => "Эхлэх огноо {$attStatusName}-ийн хувьд {$minimumDateFormatted}-ээс эрт байж болохгүй."
                    ];
                }
            }

            return ['valid' => true, 'message' => ''];

        } catch (\Exception $e) {
            \Log::error('Error validating vacation request: ' . $e->getMessage());
            return ['valid' => true, 'message' => ''];
        }
    }

    /**
     * Validate furlough duration using ChronoRange configuration
     *
     * @param string $beginDate
     * @param string|null $endDate
     * @param string $configValue
     * @param int $attStatus
     * @return array ['valid' => bool, 'message' => string]
     */
    private function validateFurloughDuration($beginDate, $endDate, $configValue, $attStatus)
    {
        try {
            // Check if endDate is provided
            if (!$endDate) {
                // No end date provided, cannot validate duration
                return ['valid' => true, 'message' => ''];
            }

            // Parse the range formula from value field
            $rangeData = ChronoRangeComponent::parseFormula($configValue);

            if (!$rangeData['type'] || $rangeData['from'] === null || $rangeData['to'] === null) {
                // Invalid formula, allow the request
                return ['valid' => true, 'message' => ''];
            }

            $beginDateCarbon = Carbon::parse($beginDate);
            $endDateCarbon = Carbon::parse($endDate);

            // Calculate the difference between end_date and begin_date
            $actualDuration = $this->calculateDurationDifference($beginDateCarbon, $endDateCarbon, $rangeData['type']);

            if ($actualDuration === null) {
                // Could not calculate duration, allow the request
                return ['valid' => true, 'message' => ''];
            }

            $minDuration = $rangeData['from'];
            $maxDuration = $rangeData['to'];

            // Check if duration is within the configured range
            if ($actualDuration < $minDuration || $actualDuration > $maxDuration) {
                $attStatusName = $this->getAttStatusName($attStatus);
                $typeName = $this->getChronoTypeName($rangeData['type']);

                return [
                    'valid' => false,
                    'message' => "Эхлэх болон дуусах огнооны зөрүү {$attStatusName}-ийн хувьд {$minDuration}-{$maxDuration} {$typeName} байх ёстой. Одоогийн зөрүү: {$actualDuration} {$typeName}"
                ];
            }

            return ['valid' => true, 'message' => ''];

        } catch (\Exception $e) {
            \Log::error('Error validating furlough duration: ' . $e->getMessage());
            return ['valid' => true, 'message' => ''];
        }
    }

    /**
     * Calculate duration difference between two dates based on chrono type
     *
     * @param Carbon $beginDate
     * @param Carbon $endDate
     * @param string $type
     * @return int|null
     */
    private function calculateDurationDifference($beginDate, $endDate, $type)
    {
        try {
            switch ($type) {
                case 'minute':
                    return $beginDate->diffInMinutes($endDate);
                case 'hour':
                    return $beginDate->diffInHours($endDate);
                case 'halfday':
                    return intval($beginDate->diffInHours($endDate) / 12);
                case 'day':
                    return $beginDate->diffInDays($endDate);
                case 'month':
                    return $beginDate->diffInMonths($endDate);
                case 'quarter':
                    return intval($beginDate->diffInMonths($endDate) / 3);
                case 'halfyear':
                    return intval($beginDate->diffInMonths($endDate) / 6);
                case 'year':
                    return $beginDate->diffInYears($endDate);
                default:
                    return null;
            }
        } catch (\Exception $e) {
            \Log::error('Error calculating duration difference: ' . $e->getMessage());
            return null;
        }
    }

    /**
     * Get chrono type name in Mongolian for error messages
     *
     * @param string $type
     * @return string
     */
    private function getChronoTypeName($type)
    {
        switch ($type) {
            case 'minute':
                return 'минут';
            case 'hour':
                return 'цаг';
            case 'halfday':
                return 'хагас өдөр';
            case 'day':
                return 'өдөр';
            case 'month':
                return 'сар';
            case 'quarter':
                return 'улирал';
            case 'halfyear':
                return 'хагас жил';
            case 'year':
                return 'жил';
            default:
                return 'хугацаа';
        }
    }

    /**
     * Validate vacation request against company work date with additional logic
     *
     * @param Carbon $beginDate
     * @param array $anchorData
     * @param int $attStatus
     * @return array ['valid' => bool, 'message' => string]
     */
    private function validateVacationWithCompanyWorkDate($beginDate, $anchorData, $attStatus)
    {
        try {
            // Get current user's employee and employee_dtl
            $cn = ConnectionService::getCNForEmployeeUser();
            $user = auth()->user();

            if (!$user) {
                // No authenticated user, allow the request
                return ['valid' => true, 'message' => ''];
            }

            $employee = Employee::on($cn)->where(Employee::PHONE, $user->phone)->first();

            if (!$employee || !$employee->employee_dtl) {
                // No employee or employee_dtl found, allow the request
                return ['valid' => true, 'message' => ''];
            }

            $employeeDtl = $employee->employee_dtl;

            if (!$employeeDtl->company_work_date) {
                // No company work date set, allow the request
                return ['valid' => true, 'message' => ''];
            }

            $companyWorkDate = Carbon::parse($employeeDtl->company_work_date);
            $shiftCount = $anchorData['shift_count'];
            $type = $anchorData['type'];

            // Calculate minimum date: company_work_date + shift_count
            $minimumDate = $this->applyShiftToDate($companyWorkDate, $type, $shiftCount);

            if (!$minimumDate) {
                // Could not calculate minimum date, allow the request
                return ['valid' => true, 'message' => ''];
            }

            // VALIDATION REQUIREMENT: Current begin_date must be greater than BOTH:
            // 1. company_work_date + shift_count (company tenure requirement)
            // 2. ALL existing_request.begin_date + shift_count (spacing between vacations)

            // First condition: begin_date > company_work_date + shift_count
            if ($beginDate->lte($minimumDate)) {
                $attStatusName = $this->getAttStatusName($attStatus);
                $minimumDateFormatted = $minimumDate->format('Y-m-d');
                $companyWorkDateFormatted = $companyWorkDate->format('Y-m-d');

                return [
                    'valid' => false,
                    'message' => "Эхлэх огноо {$attStatusName}-ийн хувьд компанид ажилласан огноо ({$companyWorkDateFormatted}) + {$shiftCount} {$type} = {$minimumDateFormatted}-ээс хойш байх ёстой."
                ];
            }

            // Second condition: begin_date > ALL existing_request.begin_date + shift_count
            $existingRequestValidation = $this->validateAgainstExistingVacationRequests($beginDate, $employee->id, $attStatus, $type, $shiftCount, $cn);
            if (!$existingRequestValidation['valid']) {
                return $existingRequestValidation;
            }

            return ['valid' => true, 'message' => ''];

        } catch (\Exception $e) {
            \Log::error('Error validating vacation with company work date: ' . $e->getMessage());
            return ['valid' => true, 'message' => ''];
        }
    }

    /**
     * Apply shift to a date based on type and shift count
     *
     * @param Carbon $date
     * @param string $type
     * @param int $shiftCount
     * @return Carbon|null
     */
    private function applyShiftToDate($date, $type, $shiftCount)
    {
        try {
            $dateCopy = $date->copy();

            switch ($type) {
                case 'minute':
                    return $dateCopy->addMinutes($shiftCount);
                case 'hour':
                    return $dateCopy->addHours($shiftCount);
                case 'halfday':
                    return $dateCopy->addHours($shiftCount * 12);
                case 'day':
                    return $dateCopy->addDays($shiftCount);
                case 'month':
                    return $dateCopy->addMonths($shiftCount);
                case 'quarter':
                    return $dateCopy->addMonths($shiftCount * 3);
                case 'halfyear':
                    return $dateCopy->addMonths($shiftCount * 6);
                case 'year':
                    return $dateCopy->addYears($shiftCount);
                default:
                    return null;
            }
        } catch (\Exception $e) {
            \Log::error('Error applying shift to date: ' . $e->getMessage());
            return null;
        }
    }

    /**
     * Validate that the new begin_date is greater than all existing vacation requests' begin_date + shift_count
     *
     * @param Carbon $beginDate
     * @param int $employeeId
     * @param int $attStatus
     * @param string $type
     * @param int $shiftCount
     * @param string $cn
     * @return array ['valid' => bool, 'message' => string]
     */
    private function validateAgainstExistingVacationRequests($beginDate, $employeeId, $attStatus, $type, $shiftCount, $cn)
    {
        try {
            // Get existing vacation requests for this employee with the same att_status
            $existingRequests = TimeRequest::on($cn)
                ->where(TimeRequest::EMPLOYEE_ID, $employeeId)
                ->where(TimeRequest::ATT_STATUS, $attStatus)
                ->whereIn(TimeRequest::CONFIRM_STATUS, [
                    TimeRequest::CONFIRM_STATUS_SENT,
                    TimeRequest::CONFIRM_STATUS_CONFIRMED,
                    TimeRequest::CONFIRM_STATUS_TEMP_CONFIRMED
                ])
                ->get();

            // Check that begin_date is greater than each existing request's begin_date + shift_count
            foreach ($existingRequests as $request) {
                $requestBeginDate = Carbon::parse($request->begin_date);
                $shiftedDate = $this->applyShiftToDate($requestBeginDate, $type, $shiftCount);

                if ($shiftedDate && $beginDate->lte($shiftedDate)) {
                    $attStatusName = $this->getAttStatusName($attStatus);
                    $requestBeginDateFormatted = $requestBeginDate->format('Y-m-d');
                    $shiftedDateFormatted = $shiftedDate->format('Y-m-d');

                    return [
                        'valid' => false,
                        'message' => "Эхлэх огноо {$attStatusName}-ийн хувьд өмнөх хүсэлтийн огноо ({$requestBeginDateFormatted}) + {$shiftCount} {$type} = {$shiftedDateFormatted}-ээс хойш байх ёстой."
                    ];
                }
            }

            return ['valid' => true, 'message' => ''];

        } catch (\Exception $e) {
            \Log::error('Error validating against existing vacation requests: ' . $e->getMessage());
            return ['valid' => true, 'message' => ''];
        }
    }



    /**
     * Calculate minimum allowed date based on anchor configuration
     *
     * @param array $anchorData
     * @return Carbon|null
     */
    private function calculateMinimumDate($anchorData)
    {
        try {
            // Get the anchor date
            $anchorDate = null;
            
            if ($anchorData['date_source'] === 'custom' && $anchorData['custom_date']) {
                $anchorDate = Carbon::parse($anchorData['custom_date']);
            } elseif ($anchorData['date_source'] === 'now') {
                $anchorDate = Carbon::now();
            } else {
                // For other date sources (model fields), we would need to implement
                // specific logic to retrieve the date from the model
                // For now, default to 'now' if not custom
                $anchorDate = Carbon::now();
            }

            if (!$anchorDate) {
                return null;
            }

            // Apply the shift based on type and shift_count
            $shiftCount = $anchorData['shift_count'];
            $type = $anchorData['type'];

            switch ($type) {
                case 'minute':
                    return $anchorDate->addMinutes($shiftCount);
                case 'hour':
                    return $anchorDate->addHours($shiftCount);
                case 'halfday':
                    return $anchorDate->addHours($shiftCount * 12);
                case 'day':
                    return $anchorDate->addDays($shiftCount);
                case 'month':
                    return $anchorDate->addMonths($shiftCount);
                case 'quarter':
                    return $anchorDate->addMonths($shiftCount * 3);
                case 'halfyear':
                    return $anchorDate->addMonths($shiftCount * 6);
                case 'year':
                    return $anchorDate->addYears($shiftCount);
                default:
                    return null;
            }

        } catch (\Exception $e) {
            \Log::error('Error calculating minimum date: ' . $e->getMessage());
            return null;
        }
    }

    /**
     * Validate count and quarter-based logic for salary furlough configurations
     *
     * @param string $beginDate
     * @param array $countData
     * @param int $attStatus
     * @return array ['valid' => bool, 'message' => string]
     */
    private function validateCountAndQuarter($beginDate, $countData, $attStatus)
    {
        try {
            $beginDateCarbon = Carbon::parse($beginDate);

            // First param (count) means last current type of request is in between date range
            // Begin date should be greater than current session end date
            $count = $countData['count'];
            $chronoType = $countData['chrono_type']; // Second param (quarter, month, halfyear, year, etc.)

            // Handle different chrono types
            $supportedChronoTypes = ['month', 'quarter', 'halfyear', 'year'];

            if (in_array($chronoType, $supportedChronoTypes)) {
                // Calculate current session end date based on chrono type
                $currentSessionEndDate = $this->getCurrentSessionEndDate($beginDateCarbon, $chronoType);

                if (!$currentSessionEndDate) {
                    // Could not calculate session end date, allow the request
                    return ['valid' => true, 'message' => ''];
                }

                // Check if begin date is greater than current session end date
                if ($beginDateCarbon->lte($currentSessionEndDate)) {
                    $attStatusName = $this->getAttStatusName($attStatus);
                    $sessionEndFormatted = $currentSessionEndDate->format('Y-m-d');
                    $sessionTypeName = $this->getSessionTypeName($chronoType);
                    return [
                        'valid' => false,
                        'message' => "Эхлэх огноо {$attStatusName}-ийн хувьд одоогийн {$sessionTypeName} дуусах огноо {$sessionEndFormatted}-ээс хойш байх ёстой."
                    ];
                }

                // Additional validation: check if count limit is exceeded in the current period
                $validationResult = $this->validateCountLimit($beginDateCarbon, $count, $chronoType, $attStatus);
                if (!$validationResult['valid']) {
                    return $validationResult;
                }
            }

            return ['valid' => true, 'message' => ''];

        } catch (\Exception $e) {
            \Log::error('Error validating count and quarter: ' . $e->getMessage());
            return ['valid' => true, 'message' => ''];
        }
    }

    /**
     * Get the end date of the current session based on the given date and chrono type
     *
     * @param Carbon $date
     * @param string $chronoType
     * @return Carbon|null
     */
    private function getCurrentSessionEndDate($date, $chronoType)
    {
        $month = $date->month;
        $year = $date->year;

        switch ($chronoType) {
            case 'month':
                // End of current month
                return Carbon::create($year, $month)->endOfMonth();

            case 'quarter':
                if ($month <= 3) {
                    // Q1: January - March
                    return Carbon::create($year, 3, 31);
                } elseif ($month <= 6) {
                    // Q2: April - June
                    return Carbon::create($year, 6, 30);
                } elseif ($month <= 9) {
                    // Q3: July - September
                    return Carbon::create($year, 9, 30);
                } else {
                    // Q4: October - December
                    return Carbon::create($year, 12, 31);
                }

            case 'halfyear':
                if ($month <= 6) {
                    // First half: January - June
                    return Carbon::create($year, 6, 30);
                } else {
                    // Second half: July - December
                    return Carbon::create($year, 12, 31);
                }

            case 'year':
                // End of current year
                return Carbon::create($year, 12, 31);

            default:
                return null;
        }
    }

    /**
     * Get the session type name in Mongolian for error messages
     *
     * @param string $chronoType
     * @return string
     */
    private function getSessionTypeName($chronoType)
    {
        switch ($chronoType) {
            case 'month':
                return 'сарын';
            case 'quarter':
                return 'улирлын';
            case 'halfyear':
                return 'хагас жилийн';
            case 'year':
                return 'жилийн';
            default:
                return 'хугацааны';
        }
    }

    /**
     * Validate if the count limit is exceeded for the given period
     *
     * @param Carbon $beginDate
     * @param int $count
     * @param string $chronoType
     * @param int $attStatus
     * @return array ['valid' => bool, 'message' => string]
     */
    private function validateCountLimit($beginDate, $count, $chronoType, $attStatus)
    {
        try {
            // Get the period start and end dates based on chrono type
            $periodDates = $this->getPeriodDates($beginDate, $chronoType);

            if (!$periodDates) {
                return ['valid' => true, 'message' => ''];
            }

            // Get current user's employee ID
            $cn = ConnectionService::getCNForEmployeeUser();
            $user = auth()->user();

            if (!$user) {
                // No authenticated user, allow the request
                return ['valid' => true, 'message' => ''];
            }

            $employee = Employee::on($cn)->where(Employee::PHONE, $user->phone)->first();

            if (!$employee) {
                // No employee found, allow the request
                return ['valid' => true, 'message' => ''];
            }

            // Count existing TimeRequest records with same att_status within the period
            $existingRequestsCount = TimeRequest::on($cn)
                ->where(TimeRequest::EMPLOYEE_ID, $employee->id)
                ->where(TimeRequest::ATT_STATUS, $attStatus)
                ->where(TimeRequest::BEGIN_DATE, '>=', $periodDates['start'])
                ->where(TimeRequest::BEGIN_DATE, '<=', $periodDates['end'])
                ->whereIn(TimeRequest::CONFIRM_STATUS, [
                    TimeRequest::CONFIRM_STATUS_SENT,
                    TimeRequest::CONFIRM_STATUS_CONFIRMED,
                    TimeRequest::CONFIRM_STATUS_TEMP_CONFIRMED
                ]) // Only count non-cancelled requests
                ->count();

            // Check if adding this request would exceed the limit
            // count parameter is the maximum allowed requests
            if ($existingRequestsCount >= $count) {
                $attStatusName = $this->getAttStatusName($attStatus);
                $sessionTypeName = $this->getSessionTypeName($chronoType);
                $periodStart = $periodDates['start']->format('Y-m-d');
                $periodEnd = $periodDates['end']->format('Y-m-d');

                return [
                    'valid' => false,
                    'message' => "{$attStatusName}-ийн хүсэлтийн тоо одоогийн {$sessionTypeName} хугацаанд ({$periodStart} - {$periodEnd}) хязгаарлагдсан тооноос ({$count}) хэтэрсэн байна. Одоогийн тоо: {$existingRequestsCount}"
                ];
            }

            return ['valid' => true, 'message' => ''];

        } catch (\Exception $e) {
            \Log::error('Error validating count limit: ' . $e->getMessage());
            return ['valid' => true, 'message' => ''];
        }
    }

    /**
     * Get period start and end dates based on chrono type
     *
     * @param Carbon $date
     * @param string $chronoType
     * @return array|null ['start' => Carbon, 'end' => Carbon]
     */
    private function getPeriodDates($date, $chronoType)
    {
        $month = $date->month;
        $year = $date->year;

        switch ($chronoType) {
            case 'month':
                return [
                    'start' => Carbon::create($year, $month, 1),
                    'end' => Carbon::create($year, $month)->endOfMonth()
                ];

            case 'quarter':
                if ($month <= 3) {
                    return [
                        'start' => Carbon::create($year, 1, 1),
                        'end' => Carbon::create($year, 3, 31)
                    ];
                } elseif ($month <= 6) {
                    return [
                        'start' => Carbon::create($year, 4, 1),
                        'end' => Carbon::create($year, 6, 30)
                    ];
                } elseif ($month <= 9) {
                    return [
                        'start' => Carbon::create($year, 7, 1),
                        'end' => Carbon::create($year, 9, 30)
                    ];
                } else {
                    return [
                        'start' => Carbon::create($year, 10, 1),
                        'end' => Carbon::create($year, 12, 31)
                    ];
                }

            case 'halfyear':
                if ($month <= 6) {
                    return [
                        'start' => Carbon::create($year, 1, 1),
                        'end' => Carbon::create($year, 6, 30)
                    ];
                } else {
                    return [
                        'start' => Carbon::create($year, 7, 1),
                        'end' => Carbon::create($year, 12, 31)
                    ];
                }

            case 'year':
                return [
                    'start' => Carbon::create($year, 1, 1),
                    'end' => Carbon::create($year, 12, 31)
                ];

            default:
                return null;
        }
    }

    /**
     * Get attendance status name for error messages using existing EmployeeAttendanceDtl method
     *
     * @param int $attStatus
     * @return string
     */
    private function getAttStatusName($attStatus)
    {
        // Create a temporary EmployeeAttendanceDtl instance to use the existing getAttStatusName method
        $employeeAttendanceDtl = app(EmployeeAttendanceDtl::class);
        $employeeAttendanceDtl->attendance_status = $attStatus;

        $statusName = $employeeAttendanceDtl->getAttStatusName();

        return $statusName ?: 'Тодорхойгүй төрөл';
    }
}
